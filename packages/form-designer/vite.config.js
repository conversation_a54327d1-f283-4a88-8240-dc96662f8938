import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJSX from '@vitejs/plugin-vue-jsx'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith('fc-')
        }
      }
    }),
    vueJSX()
  ],
  // 开发服务器配置
  server: {
    port: 8080,
    // open: true,
    hmr: {
      overlay: false
    }
  },
  // 开发环境优化
  // optimizeDeps: {
  //   include: [
  //     'vue',
  //     'element-plus',
  //     'vuedraggable',
  //     '@form-create/element-ui',
  //     '@form-create/component-wangeditor',
  //     'codemirror',
  //     'js-beautify'
  //   ]
  // },
  // 路径解析
  resolve: {
    alias: {
      '@': '/src'
    }
  }
})
