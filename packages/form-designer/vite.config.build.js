import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJSX from '@vitejs/plugin-vue-jsx'

const pkg = {
    name: 'form-designer',
    version: '1.0.0',
    author: 'form-designer team',
    license: 'MIT'
}



// https://vitejs.dev/config/
export default defineConfig({
    build: {
        lib: {
            entry: 'src/index.js',
            name: 'FormDesigner',
            fileName: format => `index.${format}.js`,
        },
        rollupOptions: {
            output: {
                exports: 'named',
                globals: {
                    vue: 'Vue',
                    'element-plus': 'ElementPlus',
                    '@form-create/element-ui': 'formCreate'
                }
            },
            external: [
                'vue',
                'element-plus',
                '@form-create/element-ui'
            ],
        },
        // 启用压缩和优化
        minify: 'terser',
        // 启用 gzip 大小报告
        reportCompressedSize: true,
        // 代码分割优化
        chunkSizeWarningLimit: 1000,
    },

    // 性能优化配置
    optimizeDeps: {
        include: [
            'vue',
            'element-plus',
            'vuedraggable',
            '@form-create/element-ui'
        ]
    },
    plugins: [
        vue({
            // 启用生产优化
            template: {
                compilerOptions: {
                    isCustomElement: (tag) => tag.startsWith('fc-')
                }
            }
        }),
        vueJSX()
    ]
})
